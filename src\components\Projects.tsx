import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { useState, useEffect } from 'react';
import { ThemeColors, defaultTheme, getThemeClasses, getThemeStyles, getThemeColor } from '../utils/themeUtils';

interface ProjectsProps {
  theme?: ThemeColors;
}

const Projects = ({ theme = defaultTheme }: ProjectsProps) => {
  // Get theme classes and styles
  const classes = getThemeClasses(theme);
  const styles = getThemeStyles(theme);
  const projects = [
    {
      title: "Analytics Pipeline",
      description: "An end-to-end data processing pipeline designed to efficiently ingest, transform, and store data across various services for analytics purposes.",
      technologies: ["Azure Data Factory", "Azure Synapse Analytics", "Azure Databricks", "Azure Blob Storage", "SQL Server", "Azure Functions", "C#", "Python"],
      highlights: [
        "Designed and implemented end-to-end data processing pipeline",
        "Orchestrated data movement and workflow automation with Azure Data Factory",
        "Developed ETL processes in Azure Databricks for data transformation",
        "Managed high-availability data storage using Azure Blob Storage and SQL Server",
        "Implemented using Agile methodologies for incremental improvements"
      ],
      image: "analytics-pipeline.jpg", // Placeholder image name
      color: theme.secondary
    },
    {
      title: "IVY Enterprise",
      description: "A .NET web application for restaurant data management with enterprise-level features, comprehensive reporting capabilities, and secure authentication.",
      technologies: ["ASP.NET Core 3.1", "C#", "LINQ", "Entity Framework", "JavaScript", "Web API", "Angular", "Azure Functions 6.0", "Azure SQL Database", "Azure App Service", "Azure Key Vault", "Azure Data Factory", "Azure Application Insights", "Azure Active Directory", "Azure Blob Storage", "Power BI", "DAX"],
      highlights: [
        "Developed enterprise-level restaurant data management application",
        "Created user-friendly dashboard for intuitive data access and management",
        "Implemented comprehensive reporting capabilities using Power BI",
        "Integrated with Microsoft Identity for secure authentication",
        "Utilized Agile methodologies for continuous optimization"
      ],
      image: "ivy-enterprise.jpg", // Placeholder image name
      color: theme.secondary
    },
    {
      title: "Oculus CRM",
      description: "A Customer Relationship Management system that handles candidate, contact, job, and company information for streamlined business operations.",
      technologies: ["C#", ".NET Core", "jQuery", "JavaScript", "HTML", "CSS", "Azure Key Vault", "Azure SQL Database", "Azure App Service"],
      highlights: [
        "Created comprehensive CRM system for business relationship management",
        "Implemented candidate tracking and management features",
        "Developed contact and company information management",
        "Built job tracking and management functionality",
        "Deployed using Azure DevOps CI/CD pipeline"
      ],
      image: "oculus-crm.jpg", // Placeholder image name
      color: theme.secondary
    },
    {
      title: "Open Table Booking System",
      description: "A serverless solution for automating data transfer between Open Table API and Azure storage, with a companion system for booking modification and deletion.",
      technologies: ["C#", "Azure Functions 6.0", "REST API", "Azure SQL Database", "Azure App Service", "Azure Key Vault", "Azure Application Insights", "Azure Blob Storage", "Blazor", "JavaScript", "SQL Server"],
      highlights: [
        "Created Azure Function for automating booking data transfer",
        "Connected to Open Table API for retrieving booking data",
        "Implemented data storage in Azure Table storage",
        "Developed user-friendly interface for booking modification and deletion",
        "Ensured high performance and security standards"
      ],
      image: "open-table.jpg", // Placeholder image name
      color: theme.secondary
    },
    {
      title: "Women Power Helpline",
      description: "A case management website for the Authority of Uttar Pradesh that manages and tracks progress of cases, allowing victims or relatives to lodge complaints through a call center.",
      technologies: [".NET MVC", "jQuery", "Ajax", "JSON", "HTML/CSS", "HTML to PDF", "jQuery data Tables", "Data export to Excel/CSV", "HighChart.js", "Google Map API", "Leaflet js", "jQuery calendar", "SQL Server 2008"],
      highlights: [
        "Developed case management system for tracking complaint progress",
        "Implemented call center integration for complaint lodging",
        "Created ASP.NET MVC API for Android development",
        "Built data visualization with HighChart.js and mapping with Google Maps API",
        "Enabled data export to various formats including Excel/CSV and PDF"
      ],
      image: "women-power-helpline.jpg", // Placeholder image name
      color: theme.secondary
    }
  ];

  // State for the slider
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Auto-slide functionality
  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        nextSlide();
      }, 4000); // Change slide every 4 seconds for smoother experience

      return () => clearInterval(interval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentIndex, isPaused]);

  // Navigation functions
  const nextSlide = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) =>
      prevIndex === projects.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? projects.length - 1 : prevIndex - 1
    );
  };

  // Enhanced animation variants for continuous smooth transitions
  const sliderVariants = {
    incoming: (direction: number) => ({
      x: direction > 0 ? "30%" : "-30%",
      opacity: 0,
      scale: 0.9,
      filter: "blur(4px)",
      transition: {
        x: { type: "tween", ease: "easeInOut", duration: 0.8 },
        opacity: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        scale: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        filter: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
      }
    }),
    active: {
      x: 0,
      opacity: 1,
      scale: 1,
      filter: "blur(0px)",
      transition: {
        x: { type: "tween", ease: "easeInOut", duration: 0.8 },
        opacity: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        scale: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        filter: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
      }
    },
    outgoing: (direction: number) => ({
      x: direction > 0 ? "-30%" : "30%",
      opacity: 0,
      scale: 0.9,
      filter: "blur(4px)",
      transition: {
        x: { type: "tween", ease: "easeInOut", duration: 0.8 },
        opacity: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        scale: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
        filter: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
      }
    })
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  // Function to render a single project card
  const renderProject = (project: {
    title: string;
    description: string;
    technologies: string[];
    highlights: string[];
    image: string;
    color: string;
  }) => {
    return (
      <motion.div
        className="bg-gray-800/50 backdrop-blur-sm rounded-2xl overflow-hidden shadow-xl transition-all duration-700 group w-full max-w-sm lg:max-w-md flex flex-col min-h-[600px] sm:min-h-[650px] md:min-h-[700px] lg:min-h-[600px]"
        style={{
          boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
        }}
        variants={itemVariants}
        whileHover={{
          y: -15,
          boxShadow: `0 20px 25px -5px ${getThemeColor(theme, 'shadow')}, 0 10px 10px -5px ${getThemeColor(theme, 'shadow')}`
        }}
        transition={{
          type: "tween",
          ease: [0.25, 0.1, 0.25, 1],
          duration: 0.5
        }}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <motion.div
          className={`h-48 sm:h-52 md:h-56 bg-gradient-to-r ${project.color} relative overflow-hidden`}
          initial={{ opacity: 0.9 }}
          whileHover={{ opacity: 1 }}
        >
          {/* Project image or placeholder with animated icon */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center text-white text-6xl"
            initial={{ scale: 0.9 }}
            animate={{
              scale: [0.9, 1, 0.9],
              rotate: [0, 5, 0, -5, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            🖥️
          </motion.div>

          {/* Enhanced overlay with links */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-b from-black/80 to-black/95 opacity-0 group-hover:opacity-100 flex items-center justify-center gap-8 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <motion.a
              href="#"
              className="w-14 h-14 text-white rounded-full flex items-center justify-center shadow-lg"
              style={{
                ...styles.gradientBg,
                boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
              }}
              whileHover={{ scale: 1.2, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              aria-label="View code on GitHub"
            >
              <FaGithub className="w-7 h-7" />
            </motion.a>
            <motion.a
              href="#"
              className="w-14 h-14 text-white rounded-full flex items-center justify-center shadow-lg"
              style={{
                ...styles.gradientBg,
                boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
              }}
              whileHover={{ scale: 1.2, rotate: -5 }}
              whileTap={{ scale: 0.9 }}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              aria-label="View live project"
            >
              <FaExternalLinkAlt className="w-6 h-6" />
            </motion.a>
          </motion.div>
        </motion.div>

        <motion.div
          className="p-4 sm:p-6 md:p-8 flex-1 flex flex-col"
          initial={{ opacity: 0.8 }}
          whileHover={{ opacity: 1 }}
        >
          <motion.h3
            className={`text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3 ${classes.titleGradientClass}`}
            initial={{ y: -5 }}
            whileInView={{ y: 0 }}
            transition={{ type: "spring", stiffness: 500 }}
          >
            {project.title}
          </motion.h3>
          <motion.p
            className="text-gray-300 mb-4 sm:mb-6 text-sm sm:text-base line-clamp-3"
            initial={{ opacity: 0.7 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {project.description}
          </motion.p>

          <motion.div
            className="mb-4 sm:mb-6 flex-1"
            initial={{ opacity: 0.7 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h4 className={`font-semibold mb-2 sm:mb-3 text-sm sm:text-base ${classes.accentTextClass}`}>Key Highlights:</h4>
            <ul className="space-y-1 sm:space-y-2">
              {project.highlights.slice(0, 4).map((highlight, hIndex) => (
                <motion.li
                  key={hIndex}
                  className="flex items-start gap-2 text-gray-300 text-xs sm:text-sm"
                  initial={{ x: -10, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.1 * hIndex }}
                >
                  <span className="inline-block w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0" style={styles.gradientBg}></span>
                  <span className="leading-tight">{highlight}</span>
                </motion.li>
              ))}
              {project.highlights.length > 4 && (
                <motion.li
                  className={`${classes.accentTextClass} text-xs sm:text-sm mt-1 pl-3 sm:pl-4`}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  +{project.highlights.length - 4} more highlights
                </motion.li>
              )}
            </ul>
          </motion.div>

          <motion.div
            className="mt-auto"
            initial={{ opacity: 0.7 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <h4 className={`font-semibold mb-2 sm:mb-3 text-sm sm:text-base ${classes.accentTextClass}`}>Technologies:</h4>
            <div className="flex flex-wrap gap-1 sm:gap-2">
              {project.technologies.slice(0, 6).map((tech, tIndex) => (
                <motion.span
                  key={tIndex}
                  className="text-white text-xs font-medium px-2 sm:px-3 py-1 sm:py-1.5 rounded-full shadow-sm"
                  style={styles.gradientBg}
                  initial={{ scale: 0.8, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ delay: 0.05 * tIndex }}
                >
                  {tech}
                </motion.span>
              ))}
              {project.technologies.length > 6 && (
                <motion.span
                  className="text-white text-xs font-medium px-2 sm:px-3 py-1 sm:py-1.5 rounded-full shadow-sm"
                  style={styles.gradientBg}
                  initial={{ scale: 0.8, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ delay: 0.3 }}
                >
                  +{project.technologies.length - 6} more
                </motion.span>
              )}
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <section id="projects" className="py-12 sm:py-16 md:py-20 text-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-8 sm:mb-12 md:mb-16 text-center"
        >
          <h2 className={`text-3xl sm:text-4xl md:text-5xl font-bold mb-3 sm:mb-4 ${classes.titleGradientClass}`}>
            Featured Projects
          </h2>
          <p className="text-base sm:text-lg text-gray-300 max-w-3xl mx-auto px-4">
            Here are some of the significant projects I've worked on, showcasing my expertise in Azure cloud services, data pipelines, and enterprise applications.
          </p>
        </motion.div>

        <div className="relative mt-12">
          {/* Slider container */}
          <div
            className="overflow-hidden group"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            {/* Enhanced slider indicators */}
            <div className="flex justify-center mt-8 mb-4 space-x-3">
              {projects.map((_, index) => (
                <motion.button
                  type="button"
                  key={index}
                  onClick={() => {
                    setDirection(index > currentIndex ? 1 : -1);
                    setCurrentIndex(index);
                  }}
                  className={`h-3 rounded-full transition-all duration-500 ${
                    index === currentIndex
                      ? 'w-8 shadow-lg'
                      : 'bg-gray-700 hover:bg-gray-500 w-3'
                  }`}
                  style={index === currentIndex ? {
                    ...styles.gradientBg,
                    boxShadow: `0 4px 6px -1px ${getThemeColor(theme, 'shadow')}`
                  } : {}}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>

            {/* Slider content */}
            <div className="relative min-h-[700px] sm:min-h-[750px] md:min-h-[800px] lg:min-h-[650px]">
              <AnimatePresence initial={false} custom={direction} mode="popLayout">
                <motion.div
                  key={currentIndex}
                  custom={direction}
                  variants={sliderVariants}
                  initial="incoming"
                  animate="active"
                  exit="outgoing"
                  className="absolute inset-0 flex items-start justify-center py-4"
                  transition={{ duration: 0.8 }}
                >
                  {/* Responsive grid layout */}
                  <div className="w-full max-w-7xl mx-auto px-4">
                    {/* For small screens (1 project) */}
                    <div className="grid grid-cols-1 gap-6 md:hidden">
                      <div className="flex justify-center">
                        {renderProject(projects[currentIndex])}
                      </div>
                    </div>

                    {/* For medium screens (2 projects) */}
                    <div className="hidden md:grid lg:hidden grid-cols-2 gap-6 items-start">
                      {renderProject(projects[currentIndex])}
                      {renderProject(projects[(currentIndex + 1) % projects.length])}
                    </div>

                    {/* For large screens (3 projects) */}
                    <div className="hidden lg:grid grid-cols-3 gap-6 items-start">
                      {renderProject(projects[currentIndex])}
                      {renderProject(projects[(currentIndex + 1) % projects.length])}
                      {renderProject(projects[(currentIndex + 2) % projects.length])}
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Enhanced navigation buttons */}
            <div className="absolute inset-0 pointer-events-none">
              <motion.button
                type="button"
                onClick={prevSlide}
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 text-white p-3 sm:p-4 rounded-full z-10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-auto"
                style={{
                  ...styles.gradientBg,
                  boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
                }}
                whileHover={{ scale: 1.1, x: -5 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Previous project"
              >
                <FaChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
              </motion.button>
              <motion.button
                type="button"
                onClick={nextSlide}
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 text-white p-3 sm:p-4 rounded-full z-10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-auto"
                style={{
                  ...styles.gradientBg,
                  boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
                }}
                whileHover={{ scale: 1.1, x: 5 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Next project"
              >
                <FaChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
