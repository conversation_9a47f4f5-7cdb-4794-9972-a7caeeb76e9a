{"name": "developer-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"animejs": "^3.2.1", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.11.0"}, "devDependencies": {"@eslint/js": "^8.57.0", "@types/animejs": "^3.1.13", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "globals": "^13.24.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "typescript-eslint": "^7.0.2", "vite": "^6.3.4"}}