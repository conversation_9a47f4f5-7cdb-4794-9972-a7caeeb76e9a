import { motion } from 'framer-motion';
import { FaGithub, FaLinkedinIn, FaTwitter, FaFacebookF, FaHeart } from 'react-icons/fa';

interface FooterProps {
  theme?: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
  };
}

const Footer = ({ theme }: FooterProps = {}) => {
  const currentYear = new Date().getFullYear();



  const socialLinks = [
    { icon: <FaGithub />, url: 'https://github.com/50urceC0de', label: 'GitHub' },
    { icon: <FaLinkedinIn />, url: 'https://www.linkedin.com/in/rohit-kumar-097920161', label: 'LinkedIn' },
    { icon: <FaTwitter />, url: 'https://x.com/Rohit_0_', label: 'Twitter' },
    { icon: <FaFacebookF />, url: '#', label: 'Facebook' }
  ];

  const footerLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Skills', href: '#skills' },
    { name: 'Experience', href: '#experience' },
    { name: 'Projects', href: '#projects' },
    { name: 'Contact', href: '#contact' }
  ];

  // Get theme colors or use defaults
  const themeColors = theme || {
    primary: 'from-indigo-950 via-blue-950 to-purple-950',
    secondary: 'from-blue-500 to-indigo-600',
    accent: 'blue-400',
    text: 'blue-300'
  };

  // Get accent color for text
  const getAccentTextColor = () => {
    if (themeColors.accent === 'yellow-500') {
      return 'text-amber-500';
    }
    return `text-${themeColors.accent}`;
  };

  // Get accent color for hover
  const getAccentHoverColor = () => {
    if (themeColors.accent === 'yellow-500') {
      return 'hover:text-amber-300';
    }
    return `hover:text-${themeColors.text}`;
  };

  // Get gradient for buttons
  const getGradientClasses = () => {
    if (themeColors.accent === 'yellow-500') {
      return 'from-amber-500 to-yellow-600';
    }
    return themeColors.secondary;
  };

  return (
    <footer className={`${themeColors.accent === 'yellow-500' ? 'bg-gray-900' : 'bg-indigo-900'} text-white relative`}>
      {/* Curved top edge */}
      <div className="absolute top-0 left-0 w-full overflow-hidden leading-none">
        <svg className="relative block w-full h-12" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className={themeColors.accent === 'yellow-500' ? 'fill-gray-900' : 'fill-indigo-900'}></path>
        </svg>
      </div>



      <div className="container mx-auto px-4 pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className={`text-3xl font-bold ${getAccentTextColor()} mb-4`}>Rohit Kumar</h2>
              <p className={`${themeColors.accent === 'yellow-500' ? 'text-gray-400' : 'text-gray-300'} mb-6 max-w-md`}>
                A versatile Full Stack .NET Developer with strong expertise in Azure integration and cloud services, specializing in microservices architecture and enterprise solutions.
              </p>
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.url}
                    target={social.url.startsWith('http') ? "_blank" : undefined}
                    rel={social.url.startsWith('http') ? "noopener noreferrer" : undefined}
                    className={`bg-gray-800 p-2 rounded-lg ${themeColors.accent === 'yellow-500' ? 'text-gray-400' : 'text-gray-300'} hover:text-white hover:bg-gray-700 transition-colors`}
                    whileHover={{ y: -5 }}
                    whileTap={{ scale: 0.9 }}
                    aria-label={social.label}
                  >
                    {social.icon}
                  </motion.a>
                ))}
              </div>
            </motion.div>
          </div>

          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h3 className="text-xl font-bold text-white mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {footerLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className={`${themeColors.accent === 'yellow-500' ? 'text-gray-400' : 'text-gray-300'} ${getAccentHoverColor()} transition-colors flex items-center`}
                    >
                      <span className="mr-2">•</span>
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h3 className="text-xl font-bold text-white mb-4">Contact</h3>
              <ul className={`space-y-2 ${themeColors.accent === 'yellow-500' ? 'text-gray-400' : 'text-gray-300'}`}>
                <li>
                  <a href="mailto:<EMAIL>" className={`${getAccentHoverColor()} transition-colors`}>
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a href="tel:+************" className={`${getAccentHoverColor()} transition-colors`}>
                    91-9761643072
                  </a>
                </li>
                <li>Agra, India</li>
              </ul>
            </motion.div>
          </div>
        </div>

        <hr className={`${themeColors.accent === 'yellow-500' ? 'border-gray-800' : 'border-gray-700'} mb-8`} />

        <div className={`flex flex-col md:flex-row justify-between items-center ${themeColors.accent === 'yellow-500' ? 'text-gray-500' : 'text-gray-400'} text-sm`}>
          <p>&copy; {currentYear} Rohit Kumar. All rights reserved.</p>
          <p className="mt-4 md:mt-0 flex items-center">
            Made with <FaHeart className="text-red-500 mx-1" /> by Rohit Kumar
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
