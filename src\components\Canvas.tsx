import { useState, useEffect, useRef } from 'react';
import anime from 'animejs/lib/anime.es.js';
import Hero from './Hero';
import About from './About';
import Skills from './Skills';
import Experience from './Experience';
import Projects from './Projects';
import Contact from './Contact';
import ThemeSwitcher from './ThemeSwitcher';
import SearchBar from './SearchBar';
import Footer from './Footer';

// Define the sections for navigation
const sections = [
  { id: 'home', component: Hero, color: 'from-indigo-950 via-blue-950 to-purple-950' },
  { id: 'about', component: About, color: 'from-blue-950 via-indigo-950 to-blue-900' },
  { id: 'skills', component: Skills, color: 'from-purple-950 via-indigo-950 to-purple-900' },
  { id: 'experience', component: Experience, color: 'from-indigo-900 via-blue-950 to-indigo-950' },
  { id: 'projects', component: Projects, color: 'from-blue-900 via-purple-950 to-blue-950' },
  { id: 'contact', component: Contact, color: 'from-purple-900 via-indigo-950 to-purple-950' }
];

// Theme colors
const themes = {
  blue: {
    primary: 'from-indigo-950 via-blue-950 to-purple-950',
    secondary: 'from-blue-500 to-indigo-600',
    accent: 'blue-400',
    text: 'blue-300'
  },
  purple: {
    primary: 'from-purple-950 via-indigo-950 to-purple-900',
    secondary: 'from-purple-500 to-violet-600',
    accent: 'purple-400',
    text: 'purple-300'
  },
  teal: {
    primary: 'from-teal-950 via-blue-950 to-teal-900',
    secondary: 'from-teal-500 to-blue-600',
    accent: 'teal-400',
    text: 'teal-300'
  },
  red: {
    primary: 'from-red-950 via-rose-950 to-red-900',
    secondary: 'from-red-500 to-rose-600',
    accent: 'red-400',
    text: 'red-300'
  },
  green: {
    primary: 'from-green-950 via-emerald-950 to-green-900',
    secondary: 'from-green-500 to-emerald-600',
    accent: 'green-400',
    text: 'green-300'
  },
  white: {
    primary: 'from-gray-100 via-gray-200 to-gray-300',
    secondary: 'from-gray-400 to-gray-500',
    accent: 'gray-600',
    text: 'gray-800'
  },
  black: {
    primary: 'from-black via-gray-950 to-black',
    secondary: 'from-amber-500 to-yellow-600',
    accent: 'yellow-500',
    text: 'amber-300'
  }
};

type ThemeKey = keyof typeof themes;

const Canvas = () => {
  // State for current section and theme
  const [currentSection, setCurrentSection] = useState(0);
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>('black');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Handle theme change
  const changeTheme = (theme: ThemeKey) => {
    console.log(`Setting theme to: ${theme}`);
    setCurrentTheme(theme);

    // Apply theme to all sections by updating the className directly
    sections.forEach((section, index) => {
      const sectionEl = sectionRefs.current[index];
      if (sectionEl) {
        // Get the current classes and update the background gradient
        const currentClasses = sectionEl.className;
        // Remove old gradient classes and add new ones
        const baseClasses = 'absolute inset-0 transition-opacity duration-500 bg-gradient-to-br';
        const opacityClasses = index === currentSection ? 'opacity-100' : 'opacity-0 pointer-events-none';
        sectionEl.className = `${baseClasses} ${themes[theme].primary} ${opacityClasses}`;
      }
    });

    // Save theme preference to localStorage
    localStorage.setItem('portfolioTheme', theme);
  };

  // Load saved theme on initial render or use black as default
  useEffect(() => {
    const savedTheme = localStorage.getItem('portfolioTheme') as ThemeKey;
    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme);
    } else {
      // Set black theme as default if no saved theme
      setCurrentTheme('black');
      localStorage.setItem('portfolioTheme', 'black');
    }
  }, []);

  // Handle navigation between sections with building/drawing effect
  const navigateToSection = (index: number) => {
    if (isTransitioning || index === currentSection || index < 0 || index >= sections.length) {
      return;
    }

    setIsTransitioning(true);

    // Get current and next section elements
    const currentEl = sectionRefs.current[currentSection];
    const nextEl = sectionRefs.current[index];

    if (currentEl && nextEl) {
      // First, find all content elements in the current section to animate out
      const currentContent = currentEl.querySelectorAll('.animate-item');

      // Animate current section content out
      anime({
        targets: currentContent,
        opacity: [1, 0],
        translateY: [0, -20],
        easing: 'easeOutQuad',
        duration: 400,
        delay: anime.stagger(50),
        complete: () => {
          // Hide current section
          currentEl.classList.add('opacity-0', 'pointer-events-none');

          // Show next section
          nextEl.classList.remove('opacity-0', 'pointer-events-none');

          // Find all content elements in the next section to animate in
          const nextContent = nextEl.querySelectorAll('.animate-item');

          // Reset initial state of next content
          anime.set(nextContent, {
            opacity: 0,
            translateY: 20
          });

          // Animate next section content in
          anime({
            targets: nextContent,
            opacity: [0, 1],
            translateY: [20, 0],
            easing: 'easeOutElastic(1, .6)',
            duration: 800,
            delay: anime.stagger(100),
            complete: () => {
              setCurrentSection(index);
              setIsTransitioning(false);
            }
          });
        }
      });
    }
  };

  // We've removed the wheel, touch, and keyboard navigation
  // Now navigation is only handled through the search bar

  return (
    <div
      ref={canvasRef}
      className="fixed inset-0 overflow-hidden"
    >
      {/* Theme switcher */}
      <ThemeSwitcher
        currentTheme={currentTheme}
        themes={Object.keys(themes) as ThemeKey[]}
        onThemeChange={changeTheme}
      />

      {/* Sections container */}
      <div className="w-full h-full">
        {sections.map((Section, index) => (
          <div
            key={Section.id}
            ref={el => sectionRefs.current[index] = el}
            className={`absolute inset-0 ${
              index === currentSection ? 'opacity-100' : 'opacity-0 pointer-events-none'
            } transition-opacity duration-500 bg-gradient-to-br ${themes[currentTheme].primary}`}
            style={{
              height: '100vh',
              width: '100vw',
              overflow: 'hidden'
            }}
            data-theme={currentTheme}
          >
            <div className="h-full w-full overflow-hidden">
              <Section.component
                theme={{
                  primary: themes[currentTheme].primary,
                  secondary: themes[currentTheme].secondary,
                  accent: themes[currentTheme].accent,
                  text: themes[currentTheme].text
                }}
                onNavigate={navigateToSection}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Search Bar with navigation */}
      <SearchBar
        theme={{
          primary: themes[currentTheme].primary,
          secondary: themes[currentTheme].secondary,
          accent: themes[currentTheme].accent,
          text: themes[currentTheme].text
        }}
        onNavigate={navigateToSection}
      />

      {/* Footer with theme */}
      <Footer
        theme={{
          primary: themes[currentTheme].primary,
          secondary: themes[currentTheme].secondary,
          accent: themes[currentTheme].accent,
          text: themes[currentTheme].text
        }}
      />
    </div>
  );
};

export default Canvas;
