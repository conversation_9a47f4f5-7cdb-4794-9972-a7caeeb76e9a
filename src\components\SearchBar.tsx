import { useState, useRef, useEffect } from 'react';
import { FaPaperPlane, FaMicrophone, FaSearch } from 'react-icons/fa';
import { ThemeColors } from '../utils/themeUtils';
import anime from 'animejs/lib/anime.es.js';

interface SearchBarProps {
  theme: ThemeColors;
  onNavigate: (sectionIndex: number) => void;
}

const SearchBar = ({ theme, onNavigate }: SearchBarProps) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Available sections for suggestions
  const suggestions = [
    { name: 'Home', index: 0 },
    { name: 'About', index: 1 },
    { name: 'Skills', index: 2 },
    { name: 'Experience', index: 3 },
    { name: 'Projects', index: 4 },
    { name: 'Get in Touch', index: 5 }
  ];

  // Handle input change and filter suggestions
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim()) {
      // Filter suggestions based on input
      const filtered = suggestions
        .map(s => s.name)
        .filter(s => s.toLowerCase().includes(value.toLowerCase()));
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      // Show all suggestions when input is empty
      setFilteredSuggestions(suggestions.map(s => s.name));
      setShowSuggestions(true);
    }
  };

  // Handle suggestion selection
  const handleSuggestionClick = (suggestion: string) => {
    const selectedSection = suggestions.find(s => s.name === suggestion);
    if (selectedSection) {
      // Animate eraser effect before navigating
      animateEraseEffect(() => {
        onNavigate(selectedSection.index);
      });
    }
    setInputValue('');
    setShowSuggestions(false);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      // Check if input matches any suggestion
      const matchedSuggestion = suggestions.find(
        s => s.name.toLowerCase() === inputValue.toLowerCase()
      );

      if (matchedSuggestion) {
        // Animate eraser effect before navigating
        animateEraseEffect(() => {
          onNavigate(matchedSuggestion.index);
        });
      }

      setInputValue('');
      setShowSuggestions(false);
    }
  };

  // Animate eraser effect
  const animateEraseEffect = (callback: () => void) => {
    // Create canvas for eraser effect
    const canvas = document.createElement('canvas');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '9999';
    canvas.style.pointerEvents = 'none';
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      callback();
      return;
    }

    // Set initial state
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Create eraser points
    const points: {x: number, y: number, radius: number}[] = [];
    const numPoints = 15;
    const duration = 1000;
    const startTime = Date.now();

    // Initialize points at random positions
    for (let i = 0; i < numPoints; i++) {
      points.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: 20 + Math.random() * 80
      });
    }

    // Animation function
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw eraser circles with globalCompositeOperation
      ctx.globalCompositeOperation = 'destination-out';

      // Update and draw points
      points.forEach(point => {
        // Move points
        point.x += (Math.random() - 0.5) * 10;
        point.y += (Math.random() - 0.5) * 10;

        // Increase radius over time
        point.radius += 5;

        // Draw eraser circle
        ctx.beginPath();
        ctx.arc(point.x, point.y, point.radius * progress * 2, 0, Math.PI * 2);
        ctx.fill();
      });

      // Reset composite operation
      ctx.globalCompositeOperation = 'source-over';

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // Animation complete
        document.body.removeChild(canvas);
        callback();
      }
    };

    // Start animation
    animate();
  };

  // Handle click outside to hide suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get theme-specific styles
  const getThemeStyles = () => {
    // For black theme (amber/gold accents)
    if (theme.accent === 'yellow-500') {
      return {
        button: 'bg-amber-500 hover:bg-amber-600',
        placeholder: 'placeholder-amber-300/50',
        border: 'border-amber-500/30',
        focusBorder: 'focus:border-amber-500',
        text: 'text-amber-300',
        hoverBg: 'hover:bg-amber-500/10'
      };
    }

    // Default styles based on theme
    return {
      button: `bg-${theme.accent} hover:bg-${theme.accent.replace('400', '500')}`,
      placeholder: `placeholder-${theme.text}/50`,
      border: `border-${theme.accent}/30`,
      focusBorder: `focus:border-${theme.accent}`,
      text: `text-${theme.text}`,
      hoverBg: `hover:bg-${theme.accent}/10`
    };
  };

  const styles = getThemeStyles();

  return (
    <div
      ref={containerRef}
      className="fixed bottom-8 left-0 right-0 mx-auto z-50 w-full flex justify-center items-center"
    >
      <div
        className="relative flex items-center justify-center search-bar-container"
        style={{ width: '50%', margin: '0 auto' }}
      >
        {/* Search bar with suggestions */}
        <form onSubmit={handleSubmit} className="w-full relative">
          <div className={`w-full relative flex items-center bg-black/90 backdrop-blur-md border-2 ${styles.border} rounded-full overflow-hidden shadow-xl`}>
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onFocus={() => {
                setIsFocused(true);
                // Always show suggestions when focused
                if (inputValue.trim()) {
                  // If there's input, show filtered suggestions
                  const filtered = suggestions
                    .map(s => s.name)
                    .filter(s => s.toLowerCase().includes(inputValue.toLowerCase()));
                  setFilteredSuggestions(filtered);
                  setShowSuggestions(filtered.length > 0);
                } else {
                  // If no input, show all suggestions
                  setFilteredSuggestions(suggestions.map(s => s.name));
                  setShowSuggestions(true);
                }
              }}
              onBlur={() => setIsFocused(false)}
              placeholder="Search sections..."
              className={`w-full py-4 px-6 bg-transparent outline-none text-base ${styles.text} ${styles.placeholder} ${styles.focusBorder}`}
            />

            <div className="flex items-center pr-4">
              <button
                type="button"
                className={`p-2 rounded-full ${styles.text} opacity-80 hover:opacity-100 transition-opacity`}
              >
                <FaSearch size={16} />
              </button>

              <button
                type="submit"
                disabled={!inputValue.trim()}
                className={`ml-2 p-2 rounded-full ${
                  inputValue.trim() ? styles.button : 'bg-gray-700'
                } text-white transition-colors duration-200`}
              >
                <FaPaperPlane size={14} />
              </button>
            </div>
          </div>

          {/* Suggestions dropdown */}
          {showSuggestions && (
            <div
              ref={suggestionsRef}
              className="absolute bottom-full mb-2 w-full bg-black/90 backdrop-blur-md border-2 border-gray-800 rounded-lg overflow-hidden shadow-xl"
            >
              {filteredSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className={`px-4 py-3 cursor-pointer ${styles.text} ${styles.hoverBg} transition-colors duration-200`}
                  onMouseDown={() => handleSuggestionClick(suggestion)}
                >
                  {suggestion}
                </div>
              ))}
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default SearchBar;
