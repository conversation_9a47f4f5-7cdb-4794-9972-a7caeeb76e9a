import { useState, useEffect } from 'react';
import anime from 'animejs/lib/anime.es.js';

interface ThemeSwitcherProps {
  currentTheme: string;
  themes: string[];
  onThemeChange: (theme: string) => void;
}

const ThemeSwitcher = ({ currentTheme, themes, onThemeChange }: ThemeSwitcherProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Update color indicator when theme changes
  useEffect(() => {
    anime({
      targets: '.theme-color-indicator',
      backgroundColor: getThemeColor(currentTheme),
      easing: 'easeOutElastic(1, .6)',
      duration: 600
    });
  }, [currentTheme]);

  const toggleOpen = () => {
    setIsOpen(!isOpen);

    // Animate the theme options
    if (!isOpen) {
      anime({
        targets: '.theme-option',
        translateY: [20, 0],
        opacity: [0, 1],
        delay: anime.stagger(50),
        easing: 'easeOutElastic(1, .6)',
        duration: 600
      });
    }
  };

  const handleThemeChange = (theme: string) => {
    if (theme !== currentTheme) {
      console.log(`Changing theme from ${currentTheme} to ${theme}`);
      onThemeChange(theme);

      // Animate the color change
      anime({
        targets: '.theme-color-indicator',
        backgroundColor: getThemeColor(theme),
        easing: 'easeOutElastic(1, .6)',
        duration: 600
      });
    }

    setIsOpen(false);
  };

  // Get a representative color for each theme
  const getThemeColor = (theme: string) => {
    switch (theme) {
      case 'blue': return '#3B82F6';
      case 'purple': return '#8B5CF6';
      case 'teal': return '#14B8A6';
      case 'red': return '#EF4444';
      case 'green': return '#10B981';
      case 'white': return '#9CA3AF'; // gray-400
      case 'black': return '#F59E0B'; // amber-500
      default: return '#3B82F6';
    }
  };

  // Get theme-specific text color
  const getThemeTextColor = (theme: string) => {
    // For black theme (amber/gold accents)
    if (theme === 'black') {
      return 'text-amber-300';
    }
    // For white theme (gray accents)
    if (theme === 'white') {
      return 'text-gray-700';
    }
    // For other themes, use their respective text colors
    switch (theme) {
      case 'blue': return 'text-blue-300';
      case 'purple': return 'text-purple-300';
      case 'teal': return 'text-teal-300';
      case 'red': return 'text-red-300';
      case 'green': return 'text-green-300';
      default: return 'text-blue-300';
    }
  };

  // Get current theme's text color
  const currentThemeTextClass = getThemeTextColor(currentTheme);

  return (
    <div className="fixed left-8 top-8 z-50">
      <button
        onClick={toggleOpen}
        className="flex items-center space-x-2 bg-white/10 backdrop-blur-md px-4 py-2 rounded-full hover:bg-white/20 transition-all duration-300"
        aria-label="Change theme color"
      >
        <div
          className="theme-color-indicator w-4 h-4 rounded-full"
          style={{ backgroundColor: getThemeColor(currentTheme) }}
        ></div>
        <span className={`${currentTheme === 'white' ? 'text-gray-700' : 'text-white'} text-sm`}>Theme</span>
      </button>

      {isOpen && (
        <div className="absolute top-12 left-0 bg-black/70 backdrop-blur-md rounded-lg p-3 mt-2 w-40">
          <div className="flex flex-col space-y-2">
            {themes.map((theme) => (
              <button
                key={theme}
                onClick={() => handleThemeChange(theme)}
                className={`theme-option flex items-center space-x-2 px-3 py-2 rounded hover:bg-white/10 transition-all duration-200 ${
                  theme === currentTheme ? 'bg-white/10' : ''
                }`}
              >
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: getThemeColor(theme) }}
                ></div>
                <span className={`${theme === 'white' ? 'text-gray-300' : 'text-white'} capitalize text-sm`}>{theme}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSwitcher;
