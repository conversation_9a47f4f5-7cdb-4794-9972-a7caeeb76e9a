import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, useEffect, useState } from 'react';
import { FaBuilding, FaCalendarAlt, FaCode, FaLaptopCode, FaServer, FaDatabase, FaCloud, FaMicrochip } from 'react-icons/fa';
import { ThemeColors, defaultTheme, getThemeClasses, getThemeStyles, getThemeColor, getComputedColor } from '../utils/themeUtils';
import anime from 'animejs/lib/anime.es.js';
import { animateExperienceSection } from '../utils/animeUtils';

interface ExperienceProps {
  theme?: ThemeColors;
}

const Experience = ({ theme = defaultTheme }: ExperienceProps) => {
  // Get theme classes and styles
  const classes = getThemeClasses(theme);
  const styles = getThemeStyles(theme);
  const containerRef = useRef<HTMLDivElement>(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Animation for the timeline progress
  const timelineHeight = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);

  // State to track connector paths
  const [connectorPaths, setConnectorPaths] = useState<string[]>([]);
  const timelineDotsRef = useRef<(HTMLDivElement | null)[]>([]);
  const experienceCardsRef = useRef<(HTMLDivElement | null)[]>([]);

  // Function to create a single S-wave path connecting all experience cards
  const calculateConnectorPaths = () => {
    const paths: string[] = [];
    const cards = experienceCardsRef.current.filter(card => card !== null);

    // If we don't have enough cards, don't create a path
    if (cards.length < 2) {
      setConnectorPaths([]);
      return;
    }

    // Get the SVG container reference for coordinate calculation
    const svgContainer = containerRef.current;
    if (!svgContainer) {
      setConnectorPaths([]);
      return;
    }

    const svgRect = svgContainer.getBoundingClientRect();

    // Create a single continuous S-wave path connecting all cards
    let singlePathD = '';

    // Get all connection points relative to SVG container
    const connectionPoints: { x: number; y: number }[] = [];

    cards.forEach((card, index) => {
      if (!card) return;

      const cardRect = card.getBoundingClientRect();

      let connectionX: number, connectionY: number;

      if (index === 0) {
        // First card (top card on right side): connect from right edge
        connectionX = cardRect.right;
        connectionY = cardRect.top + cardRect.height / 2;
      } else {
        // Second card (bottom card on left side): connect to left edge
        connectionX = cardRect.left;
        connectionY = cardRect.top + cardRect.height / 2;
      }

      // Convert to coordinates relative to SVG container
      const relativeX = connectionX - svgRect.left;
      const relativeY = connectionY - svgRect.top;

      connectionPoints.push({ x: relativeX, y: relativeY });
    });

    if (connectionPoints.length < 2) {
      setConnectorPaths([]);
      return;
    }

    // Create a simple smooth curve connecting the cards (like your red line)
    if (connectionPoints.length === 2) {
      // For two points, create a smooth S-curve that matches your red line
      const start = connectionPoints[0]; // Right edge of first card
      const end = connectionPoints[1];   // Left edge of second card

      // Calculate the horizontal distance and vertical distance
      const horizontalDistance = Math.abs(end.x - start.x);
      const verticalDistance = end.y - start.y;

      // Create control points for the exact S-curve from your red line:
      // 1. Start from right edge of first card
      // 2. Go horizontally right, then curve down in a wide loop
      // 3. Come back up to connect to left edge of second card

      // Calculate the middle point between the cards
      const midX = (start.x + end.x) / 2;
      const midY = (start.y + end.y) / 2;

      // Create a wider horizontal extension and deeper vertical curve
      const horizontalExtension = horizontalDistance * 0.8; // Extend further right
      const verticalExtension = Math.max(100, Math.abs(verticalDistance) * 1.5); // Deeper curve

      // First control point: extend horizontally right from start, then begin curving down
      const cp1x = start.x + horizontalExtension;
      const cp1y = start.y + verticalExtension * 0.3; // Start curving down

      // Second control point: create the bottom of the loop, then curve back up
      const cp2x = midX + horizontalExtension * 0.3; // Position for the loop
      const cp2y = midY + verticalExtension; // Bottom of the curve

      // Third control point: approach the end point from below
      const cp3x = end.x - horizontalExtension * 0.5;
      const cp3y = end.y + verticalExtension * 0.3;

      // Create a more complex curve using multiple cubic bezier segments
      singlePathD = `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${midX} ${midY + verticalExtension * 0.8} C ${cp3x} ${cp3y}, ${end.x - 50} ${end.y}, ${end.x} ${end.y}`;
    } else {
      // For multiple points, create smooth curves between each pair
      singlePathD = `M ${connectionPoints[0].x} ${connectionPoints[0].y}`;

      for (let i = 0; i < connectionPoints.length - 1; i++) {
        const start = connectionPoints[i];
        const end = connectionPoints[i + 1];

        // Calculate control points for smooth curve
        const controlOffset = Math.abs(end.x - start.x) * 0.4;
        const cp1x = start.x + (start.x < end.x ? controlOffset : -controlOffset);
        const cp1y = start.y;
        const cp2x = end.x - (start.x < end.x ? controlOffset : -controlOffset);
        const cp2y = end.y;

        singlePathD += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`;
      }
    }

    // Add the single S-wave path
    if (singlePathD) {
      paths.push(singlePathD);
    }

    // Also create connector paths from dots to cards (keeping existing functionality)
    const dots = timelineDotsRef.current.filter(dot => dot !== null);
    dots.forEach((dot, index) => {
      if (!dot || !experienceCardsRef.current[index]) return;

      const dotRect = dot.getBoundingClientRect();
      const cardRect = experienceCardsRef.current[index]!.getBoundingClientRect();

      // Calculate center points relative to SVG container
      const dotCenterX = (dotRect.left + dotRect.width / 2) - svgRect.left;
      const dotCenterY = (dotRect.top + dotRect.height / 2) - svgRect.top;

      // For even indices (0, 2, etc.), cards are on the right
      // For odd indices (1, 3, etc.), cards are on the left
      const isEven = index % 2 === 0;

      let cardX: number;
      let cardY: number;

      if (isEven) {
        // Right side card - connect to left edge
        cardX = cardRect.left - svgRect.left;
        cardY = (cardRect.top + cardRect.height / 2) - svgRect.top;
      } else {
        // Left side card - connect to right edge
        cardX = cardRect.right - svgRect.left;
        cardY = (cardRect.top + cardRect.height / 2) - svgRect.top;
      }

      // Calculate control points for the curve
      const controlX1 = isEven ? dotCenterX + 30 : dotCenterX - 30;
      const controlY1 = dotCenterY;
      const controlX2 = isEven ? cardX - 50 : cardX + 50;
      const controlY2 = cardY;

      // Create SVG path for dot to card connection
      const cardPath = `M ${dotCenterX} ${dotCenterY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${cardX} ${cardY}`;
      paths.push(cardPath);
    });

    setConnectorPaths(paths);
  };

  // Add effect for content scaling and animations
  useEffect(() => {
    const fitContentToViewport = () => {
      const experienceSection = document.getElementById('experience');
      if (experienceSection && contentContainerRef.current) {
        const viewportHeight = window.innerHeight;
        const contentHeight = contentContainerRef.current.scrollHeight;

        if (contentHeight > viewportHeight) {
          // Apply scaling to fit content
          const scale = Math.min(0.95, (viewportHeight * 0.9) / contentHeight);
          contentContainerRef.current.style.transform = `scale(${scale})`;
          contentContainerRef.current.style.transformOrigin = 'center center';
        } else {
          contentContainerRef.current.style.transform = 'scale(1)';
        }
      }
    };

    // Run on initial load and window resize
    fitContentToViewport();
    window.addEventListener('resize', fitContentToViewport);

    // Initialize experience section animations
    const animations = animateExperienceSection();

    // Calculate connector paths after a short delay to ensure DOM is ready
    const pathTimer = setTimeout(() => {
      calculateConnectorPaths();
    }, 500);

    // Recalculate paths on window resize
    window.addEventListener('resize', calculateConnectorPaths);

    return () => {
      window.removeEventListener('resize', fitContentToViewport);
      window.removeEventListener('resize', calculateConnectorPaths);
      clearTimeout(pathTimer);

      // Clean up animations
      if (animations && typeof anime.remove === 'function') {
        anime.remove('.experience-expand-indicator');
      }
    };
  }, []);

  // Effect to animate the single S-wave path
  useEffect(() => {
    if (connectorPaths.length === 0) return;

    // Animate the main S-wave path (first path in the array)
    const mainSWavePath = '.experience-connector-path:first-child';

    // Initial draw animation for the S-wave
    anime({
      targets: mainSWavePath,
      strokeDashoffset: [anime.setDashoffset, 0],
      easing: 'easeOutQuad',
      duration: 4000, // Longer duration for the single continuous path
      direction: 'normal',
      complete: () => {
        // After initial animation, add continuous flowing wave effect
        anime({
          targets: mainSWavePath,
          strokeDasharray: function(el: SVGPathElement) {
            const length = el.getTotalLength();
            return `${length * 0.08} ${length * 0.04}`; // Create flowing dash pattern
          },
          strokeDashoffset: function(el: SVGPathElement) {
            return -el.getTotalLength(); // Negative for forward flow
          },
          easing: 'linear',
          duration: 6000, // Slower flow for better visual effect
          loop: true,
        });
      }
    });

    // Animate the dot-to-card connector paths (if any)
    if (connectorPaths.length > 1) {
      anime({
        targets: '.experience-connector-path:not(:first-child)',
        strokeDashoffset: [anime.setDashoffset, 0],
        easing: 'easeInOutSine',
        duration: 1200,
        delay: (_, i) => 2000 + (i * 200), // Start after S-wave animation begins
        direction: 'normal',
      });
    }
  }, [connectorPaths]);

  const experiences = [
    {
      title: "Full Stack .NET Developer",
      company: "Dotsquares Technology Limited, Jaipur",
      period: "December 2021 - Present",
      icon: <FaCode className="text-white" />,
      color: theme.secondary,
      skills: ["ASP.NET Core", "Azure", "Angular", "Microservices", "Azure DevOps", "SQL Server"],
      description: [
        "Designed and implemented end-to-end data processing pipelines using Azure Data Factory, Synapse Analytics, and Databricks",
        "Developed enterprise-level restaurant management application (IVY Enterprise) with comprehensive reporting capabilities",
        "Created CRM systems to handle candidate, contact, job, and company information",
        "Implemented data synchronization solutions for Open Table booking system using Azure Functions and REST APIs",
        "Utilized Azure services including App Services, Functions, API Management, and various storage options"
      ]
    },
    {
      title: "Full Stack Developer",
      company: "Technosys Services PVT (LTD), Lucknow",
      period: "January 2019 - December 2021",
      icon: <FaLaptopCode className="text-white" />,
      color: theme.secondary,
      skills: [".NET MVC", "SQL Server", "jQuery", "JavaScript", "RESTful APIs", "HTML/CSS"],
      description: [
        "Developed Women Power Helpline case management system for the Authority of Uttar Pradesh",
        "Created ASP.NET MVC API for Android development",
        "Implemented features like HTML to PDF conversion, data export to Excel/CSV, and interactive maps",
        "Built responsive user interfaces with jQuery, Ajax, and Bootstrap",
        "Worked on all phases of System Development Life Cycle using Waterfall and Agile SCRUM methodologies"
      ]
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -45 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
        delay: 0.2
      }
    }
  };

  // Floating animation for the background elements
  const floatingAnimation = {
    y: ['-10px', '10px'],
    transition: {
      y: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut'
      }
    }
  };

  return (
    <section id="experience" className="h-screen text-white relative overflow-hidden flex items-center justify-center">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={floatingAnimation}
          className="absolute top-20 right-10 w-64 h-64 rounded-full mix-blend-multiply filter blur-3xl opacity-10"
          style={{ backgroundColor: getThemeColor(theme, 'accent') }}
        />
        <motion.div
          animate={{
            ...floatingAnimation,
            transition: {
              ...floatingAnimation.transition,
              delay: 0.5
            }
          }}
          className="absolute bottom-20 left-10 w-64 h-64 rounded-full mix-blend-multiply filter blur-3xl opacity-10"
          style={{ backgroundColor: getThemeColor(theme, 'text') }}
        />
      </div>

      <div ref={contentContainerRef} className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-6 text-center reveal"
        >
          <h2 className={`text-3xl md:text-4xl font-bold mb-2 ${classes.titleGradientClass} inline-block`}>
            Work Experience
          </h2>
          <div className="w-16 h-1 mx-auto my-3 rounded-full" style={styles.gradientBg}></div>
          <p className="text-sm md:text-base text-gray-300 max-w-3xl mx-auto">
            6+ years delivering robust solutions with .NET, Azure, and modern web technologies.
          </p>
        </motion.div>

        <div className="max-w-5xl mx-auto" ref={containerRef}>
          <div className="relative">
            {/* SVG container for connector paths */}
            <svg className="absolute inset-0 w-full h-full overflow-visible pointer-events-none">
              {connectorPaths.map((path, index) => {
                const isMainSWave = index === 0; // First path is the main S-wave

                return (
                  <path
                    key={index}
                    d={path}
                    className="experience-connector-path"
                    fill="none"
                    stroke={isMainSWave ? getComputedColor(theme.accent.split(' ')[1], 'red-500') : getComputedColor(theme.secondary.split(' ')[1], 'blue-500')}
                    strokeWidth={isMainSWave ? "4" : "2"} // Make main S-wave thicker
                    strokeDasharray="" // Solid lines for all paths (animation will handle dashing)
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    style={{
                      filter: isMainSWave ? 'drop-shadow(0 0 6px rgba(255, 255, 255, 0.3))' : 'none'
                    }}
                  />
                );
              })}
            </svg>

{/* Vertical timeline line removed as requested */}

            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  className={`mb-8 relative flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}
                  variants={itemVariants}
                >
                  {/* Timeline dot with icon */}
                  <motion.div
                    ref={el => timelineDotsRef.current[index] = el}
                    className="absolute left-0 md:left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full flex items-center justify-center z-10 shadow-lg"
                    style={styles.gradientBg}
                    variants={iconVariants}
                    whileHover={{ scale: 1.2, boxShadow: `0 0 20px ${getThemeColor(theme, 'shadow')}` }}
                  >
                    {exp.icon}
                  </motion.div>

                  {/* Content */}
                  <div className={`md:w-1/2 ${index % 2 === 0 ? 'md:pr-12 md:text-right' : 'md:pl-12'}`}>
                    <motion.div
                      ref={el => experienceCardsRef.current[index] = el}
                      className="bg-gray-800/50 backdrop-blur-sm p-4 rounded-xl shadow-xl transition-all duration-300 hover:shadow-2xl"
                      style={{
                        boxShadow: `0 10px 15px -3px ${getThemeColor(theme, 'shadow')}`
                      }}
                      whileHover={{ y: -3 }}
                    >
                      <div className={`flex items-center mb-1 gap-1 ${classes.accentTextClass} ${index % 2 === 0 ? 'md:justify-end' : ''}`}>
                        <FaCalendarAlt className={`text-xs ${index % 2 === 0 ? 'md:order-2' : ''}`} />
                        <span className="text-xs font-medium">{exp.period}</span>
                      </div>

                      <h3 className="text-lg md:text-xl font-bold text-white mb-1">{exp.title}</h3>

                      <div className={`flex items-center mb-2 gap-1 text-gray-300 ${index % 2 === 0 ? 'md:justify-end' : ''}`}>
                        <FaBuilding className={`text-xs ${index % 2 === 0 ? 'md:order-2' : ''}`} />
                        <h4 className="text-sm">{exp.company}</h4>
                      </div>

                      {/* Skills tags */}
                      <div className={`flex flex-wrap gap-1 mb-2 ${index % 2 === 0 ? 'md:justify-end' : ''}`}>
                        {exp.skills.slice(0, 3).map((skill, skillIndex) => (
                          <motion.span
                            key={skillIndex}
                            className="inline-block px-2 py-0.5 text-[10px] font-medium rounded-full text-white"
                            style={styles.gradientBg}
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            viewport={{ once: true }}
                            transition={{ delay: 0.3 + skillIndex * 0.1 }}
                            whileHover={{ scale: 1.1 }}
                          >
                            {skill}
                          </motion.span>
                        ))}
                      </div>

                      <div className={`space-y-1 text-gray-300 ${index % 2 === 0 ? 'md:text-right' : ''}`}>
                        {exp.description.slice(0, 2).map((item, itemIndex) => (
                          <motion.div
                            key={itemIndex}
                            className={`flex items-start gap-1 ${index % 2 === 0 ? 'md:flex-row-reverse' : ''}`}
                            initial={{ opacity: 0, x: index % 2 === 0 ? 20 : -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.4, delay: 0.4 + itemIndex * 0.1 }}
                          >
                            <span className="inline-block w-1.5 h-1.5 rounded-full mt-1.5 flex-shrink-0"
                                  style={styles.gradientBg}></span>
                            <span className="text-xs">{item}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>

        {/* Expandable content indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer experience-expand-indicator">
          <div className={`text-sm ${classes.accentTextClass} mb-1 opacity-70`}>View full experience</div>
          <div className="w-8 h-8 rounded-full bg-gray-800/70 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
