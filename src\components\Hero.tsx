import React, { useEffect, useRef, useState } from 'react';
import { FaGithub, FaLinkedinIn, FaTwitter, FaArrowDown, FaCode, FaDatabase, FaServer } from 'react-icons/fa';
import profileImage from '../assets/profile.png';
// Import anime.js correctly for ES6 modules
import anime from 'animejs/lib/anime.es.js';
import {
  animateHeroTitle,
  animateHeroImage,
  animateFloatingIcons,
  typeText,
  animateBackgroundBlobs,
  animateRotatingBorder,
  isAnimeAvailable
} from '../utils/animeUtils';
// Import new vivivit-style animations
import {
  animateLetters,
  createMouseFollowAnimation,
  staggeredEntrance,
  createParticleAnimation
} from '../utils/vivivitAnimations';

interface HeroProps {
  theme?: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
  };
  onNavigate?: (index: number) => void;
}

// Helper function to get computed color values
const getComputedColor = (tailwindClass: string, fallback: string): string => {
  // Extract color and intensity from Tailwind class
  const match = tailwindClass.match(/(blue|purple|indigo|teal|green|red|rose|emerald|amber|yellow|gray|black)-(\d+)/);
  if (!match) {
    if (fallback === 'black') return '#000000';
    if (fallback === 'gray-950') return '#030712';
    return fallback === 'blue-500' ? '#3B82F6' :
           fallback === 'purple-500' ? '#8B5CF6' :
           fallback === 'indigo-500' ? '#6366F1' :
           fallback === 'amber-500' ? '#F59E0B' :
           fallback === 'yellow-600' ? '#CA8A04' : '#3B82F6';
  }

  const [, color, intensity] = match;

  // Map to actual color values
  const colorMap: Record<string, Record<string, string>> = {
    blue: {
      '300': '#93C5FD', '400': '#60A5FA', '500': '#3B82F6', '600': '#2563EB', '700': '#1D4ED8'
    },
    purple: {
      '300': '#C4B5FD', '400': '#A78BFA', '500': '#8B5CF6', '600': '#7C3AED', '700': '#6D28D9'
    },
    indigo: {
      '300': '#A5B4FC', '400': '#818CF8', '500': '#6366F1', '600': '#4F46E5', '700': '#4338CA'
    },
    teal: {
      '300': '#5EEAD4', '400': '#2DD4BF', '500': '#14B8A6', '600': '#0D9488', '700': '#0F766E'
    },
    green: {
      '300': '#86EFAC', '400': '#4ADE80', '500': '#22C55E', '600': '#16A34A', '700': '#15803D'
    },
    red: {
      '300': '#FCA5A5', '400': '#F87171', '500': '#EF4444', '600': '#DC2626', '700': '#B91C1C'
    },
    rose: {
      '300': '#FDA4AF', '400': '#FB7185', '500': '#F43F5E', '600': '#E11D48', '700': '#BE123C'
    },
    emerald: {
      '300': '#6EE7B7', '400': '#34D399', '500': '#10B981', '600': '#059669', '700': '#047857'
    },
    amber: {
      '300': '#FCD34D', '400': '#FBBF24', '500': '#F59E0B', '600': '#D97706', '700': '#B45309', '800': '#92400E'
    },
    yellow: {
      '300': '#FDE047', '400': '#FACC15', '500': '#EAB308', '600': '#CA8A04', '700': '#A16207', '800': '#854D0E'
    },
    gray: {
      '900': '#111827', '950': '#030712'
    },
    black: {
      '': '#000000'
    }
  };

  return colorMap[color]?.[intensity] ||
         (fallback === 'amber-500' ? '#F59E0B' :
          fallback === 'yellow-600' ? '#CA8A04' :
          fallback === 'red-500' ? '#EF4444' :
          fallback === 'rose-500' ? '#F43F5E' :
          fallback === 'blue-500' ? '#3B82F6' :
          fallback === 'purple-500' ? '#8B5CF6' :
          fallback === 'indigo-500' ? '#6366F1' : '#F43F5E');
};

const Hero = ({ theme, onNavigate }: HeroProps) => {
  // Refs for anime.js animations
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLDivElement>(null);
  const nameRef = useRef<HTMLHeadingElement>(null);
  const roleRef = useRef<HTMLDivElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const socialsRef = useRef<HTMLDivElement>(null);
  const profileImageRef = useRef<HTMLDivElement>(null);
  const blobsRef = useRef<HTMLDivElement>(null);
  const floatingIconsRef = useRef<HTMLDivElement>(null);

  // Typing animation text
  const roles = ["Full Stack .NET Developer0", "Azure Integration Specialist", "Angular Developer", "Microservices Architect", "SQL Expert"];

  // Floating icons animation
  const floatingIcons = [
    { icon: <FaCode />, delay: 0, x: -20, y: -30 },
    { icon: <FaDatabase />, delay: 0.5, x: 20, y: 20 },
    { icon: <FaServer />, delay: 1, x: -10, y: 40 }
  ];

  // Track mouse position for animations
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const sectionRef = useRef<HTMLElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);

  // Mouse move handler
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  // Initialize animations when component mounts
  useEffect(() => {
    // Check if anime.js is available
    if (!isAnimeAvailable()) {
      console.error('anime.js is not available');
      return;
    }

    // Store animation instances for cleanup
    const animations = [];
    let mouseFollowCleanup = null;

    // Create particle animation
    if (particlesRef.current) {
      const particleAnimation = createParticleAnimation('.particles-container', 30);
      if (particleAnimation) animations.push(particleAnimation);
    }

    // No animation for the name - keep it visible
    if (nameRef.current) {
      // Make sure the name is visible immediately
      nameRef.current.style.opacity = '1';
    }

    // Staggered entrance for UI elements (vivivit style)
    const entranceAnimation = staggeredEntrance('.stagger-item', {
      translateY: [50, 0],
      opacity: [0, 1],
      delay: anime.stagger(120, {start: 300}),
      duration: 1500,
      easing: 'easeOutElastic(1, .6)'
    });
    if (entranceAnimation) animations.push(entranceAnimation);

    // Mouse follow animation for profile image (vivivit style)
    if (profileImageRef.current) {
      mouseFollowCleanup = createMouseFollowAnimation('.mouse-follow', {
        strength: 0.03,
        ease: 0.1,
        maxTranslation: 20
      });

      // Also add traditional animations
      const imageAnimation = animateHeroImage('.profile-image-container');
      if (imageAnimation) animations.push(imageAnimation);

      // Animate rotating border
      const borderAnimation = animateRotatingBorder();
      if (borderAnimation) animations.push(borderAnimation);
    }

    // Animate floating icons
    if (floatingIconsRef.current) {
      const iconsAnimation = animateFloatingIcons('.floating-icon');
      if (iconsAnimation) animations.push(iconsAnimation);
    }

    // Animate background blobs with more dynamic movement (vivivit style)
    const blobsAnimation = anime({
      targets: '.background-blob',
      translateX: () => anime.random(-50, 50),
      translateY: () => anime.random(-50, 50),
      scale: () => anime.random(0.8, 1.2),
      opacity: () => anime.random(0.1, 0.3),
      easing: 'easeInOutQuad',
      duration: 8000,
      complete: function(anim) {
        anime({
          targets: '.background-blob',
          translateX: () => anime.random(-50, 50),
          translateY: () => anime.random(-50, 50),
          scale: () => anime.random(0.8, 1.2),
          opacity: () => anime.random(0.1, 0.3),
          easing: 'easeInOutQuad',
          duration: 8000,
          complete: anim.complete
        });
      }
    });
    if (blobsAnimation) animations.push(blobsAnimation);



    // Type text animation for roles
    let currentRoleIndex = 0;
    let typeTimeout = null;

    const typeNextRole = () => {
      if (roleRef.current) {
        const textAnimation = typeText('#role-text', roles[currentRoleIndex]);
        if (textAnimation) animations.push(textAnimation);

        currentRoleIndex = (currentRoleIndex + 1) % roles.length;
        typeTimeout = setTimeout(typeNextRole, 3000);
      }
    };

    typeNextRole();

    // Clean up animations when component unmounts
    return () => {
      // Clear any pending timeouts
      if (typeTimeout) clearTimeout(typeTimeout);

      // Clean up mouse follow animation
      if (mouseFollowCleanup) mouseFollowCleanup();

      // Remove all animations
      if (isAnimeAvailable() && anime.remove) {
        // Clean up all animations
        anime.remove('.hero-title-element');
        anime.remove('.profile-image-container');
        anime.remove('.floating-icon');
        anime.remove('.background-blob');
        anime.remove('#rotating-border');
        anime.remove('#role-text');
        anime.remove('.hero-name .animated-letter');
        anime.remove('.stagger-item');
        anime.remove('.particle');
        anime.remove('.mouse-follow');
      }
    };
  }, []);

  // Get theme colors or use defaults
  const themeColors = theme || {
    primary: 'from-red-950 via-rose-950 to-red-900',
    secondary: 'from-red-500 to-rose-600',
    accent: 'red-500',
    text: 'red-300'
  };

  // Create dynamic class names based on theme
  const accentTextClass = `text-${themeColors.text}`;
  const accentBgClass = `bg-${themeColors.accent}`;
  const gradientBgClass = `bg-gradient-to-r ${themeColors.secondary}`;

  // Navigation handlers
  const handleContactClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onNavigate) {
      onNavigate(5); // Contact section is at index 5
    }
  };

  const handleProjectsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onNavigate) {
      onNavigate(4); // Projects section is at index 4
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center pt-24 pb-16 md:pt-32 md:pb-24 text-white overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 overflow-hidden" ref={blobsRef}>
        {/* Animated gradient background - updated to red theme */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZCIgeDI9IjAlIiB5Mj0iMTAwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2I5MWMxYyIgc3RvcC1vcGFjaXR5PSIwLjEiLz48c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2U1MWU0ZCIgc3RvcC1vcGFjaXR5PSIwLjA1Ii8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjZWY0NDQ0IiBzdG9wLW9wYWNpdHk9IjAuMSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiLz48L3N2Zz4=')]"></div>

        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-[0.03]" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '50px 50px'
        }}></div>

        {/* Particles container for vivivit-style animations */}
        <div className="particles-container absolute inset-0" ref={particlesRef}></div>

        {/* Animated blobs - using dynamic theme colors */}
        <div
          className="background-blob absolute -top-24 -left-24 w-96 h-96 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
          style={{ backgroundColor: getComputedColor(themeColors.accent, themeColors.accent) }}
        ></div>
        <div
          className="background-blob absolute top-96 -right-24 w-96 h-96 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
          style={{ backgroundColor: getComputedColor(themeColors.text, themeColors.text) }}
        ></div>
        <div
          className="background-blob absolute -bottom-24 left-1/2 w-96 h-96 rounded-full mix-blend-multiply filter blur-3xl opacity-20"
          style={{ backgroundColor: getComputedColor(themeColors.secondary.split(' ')[1] || themeColors.accent, themeColors.accent) }}
        ></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col md:flex-row items-center">
          <div
            className="md:w-1/2 mb-8 md:mb-0"
          >
            <div
              className={`inline-block ${themeColors.accent === 'yellow-500' ? 'bg-gradient-to-r from-amber-500 to-yellow-600' : `bg-gradient-to-r ${themeColors.secondary}`} px-4 py-1 rounded-full mb-4 hero-title-element opacity-0 stagger-item animate-item`}
              ref={titleRef}
            >
              <h2 className="text-sm md:text-base text-white font-medium">
                Welcome to my portfolio
              </h2>
            </div>

            <div
              className="mb-2 hero-title-element opacity-0 stagger-item animate-item"
              ref={subtitleRef}
            >
              <h2 className={`text-lg md:text-xl ${accentTextClass} font-medium`}>
                Hello, I'm
              </h2>
            </div>

            <h1
              className={`text-5xl md:text-6xl lg:text-7xl font-bold mb-4 ${themeColors.accent === 'yellow-500' ? 'text-amber-500' : `text-${themeColors.accent}`} animate-item`}
              ref={nameRef}
            >
              Rohit Kumar
            </h1>

            <div
              className="relative h-12 mb-6 overflow-hidden hero-title-element opacity-0 stagger-item animate-item"
              ref={roleRef}
            >
              <div id="role-text" className={`text-2xl md:text-3xl font-semibold ${accentTextClass}`}>
                {/* Text will be filled by anime.js */}
              </div>
            </div>

            <p
              className={`text-lg md:text-xl mb-8 ${themeColors.accent === 'yellow-500' ? 'text-gray-300' : 'text-gray-200'} max-w-xl hero-title-element opacity-0 stagger-item animate-item`}
              ref={descriptionRef}
            >
              <span className={`${accentTextClass} font-semibold`}>6+ years of experience</span> in building robust applications with ASP.NET Core, Azure, Angular, and SQL Server.
              Specialized in microservices architecture, data pipelines, and enterprise solutions.
            </p>

            <div
              className="flex flex-wrap gap-4 mb-8 hero-title-element opacity-0 stagger-item animate-item"
              ref={buttonsRef}
            >
              <button
                onClick={handleContactClick}
                className={`${themeColors.accent === 'yellow-500' ? 'bg-gradient-to-r from-amber-500 to-yellow-600' : `bg-gradient-to-r ${themeColors.secondary}`} hover:opacity-90 text-white px-8 py-4 rounded-lg transition duration-300 font-medium shadow-lg hover:shadow-xl hover:-translate-y-1 cursor-pointer`}
              >
                Contact Me
              </button>
              <button
                onClick={handleProjectsClick}
                className={`bg-transparent ${themeColors.accent === 'yellow-500' ? 'hover:bg-amber-500/10' : `hover:bg-${themeColors.accent}/10`} text-white px-8 py-4 rounded-lg transition duration-300 font-medium hover:-translate-y-1 cursor-pointer border border-white/20 hover:border-white/40`}
              >
                View Projects
              </button>
            </div>

            <div
              className="flex space-x-4 hero-title-element opacity-0 stagger-item animate-item"
              ref={socialsRef}
            >
              {[
                { icon: <FaGithub className="w-6 h-6" />, url: "https://github.com/50urceC0de" },
                { icon: <FaLinkedinIn className="w-6 h-6" />, url: "https://www.linkedin.com/in/rohit-kumar-097920161" },
                { icon: <FaTwitter className="w-6 h-6" />, url: "https://x.com/Rohit_0_" }
              ].map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${themeColors.accent === 'yellow-500' ? 'text-gray-300' : 'text-gray-200'} hover:${accentTextClass} transition-all p-3 bg-gray-800/30 rounded-full hover:bg-gray-800/60 backdrop-blur-sm hover:-translate-y-1 hover:scale-110`}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          <div
            className="md:w-1/2 flex justify-center relative"
            ref={floatingIconsRef}
          >
            {/* Floating tech icons */}
            {floatingIcons.map((item, index) => {
              // Replace hardcoded text colors in icons with theme-based colors
              const iconWithThemeColor = React.cloneElement(
                item.icon as React.ReactElement,
                {
                  className: `${themeColors.accent === 'yellow-500' ? 'text-amber-300' : `text-${themeColors.text}`}`
                }
              );

              return (
                <div
                  key={index}
                  className="floating-icon absolute text-3xl z-10 opacity-0 animate-item"
                  style={{
                    left: `calc(50% + ${index * 30 - 30}px)`,
                    top: `calc(50% - ${index * 20}px)`
                  }}
                >
                  {iconWithThemeColor}
                </div>
              );
            })}

            <div className="relative mouse-follow" ref={profileImageRef}>
              {/* Glowing effect - using dynamic theme colors with circular shape */}
              <div
                className={`profile-image-container absolute -inset-4 blur-2xl opacity-0 rounded-full ${
                  themeColors.accent === 'yellow-500'
                    ? 'bg-gradient-to-br from-amber-500 to-red-600'
                    : themeColors.accent === 'red-500' || themeColors.accent === 'rose-500'
                      ? 'bg-gradient-to-br from-red-500 to-rose-600'
                      : `bg-gradient-to-br ${themeColors.secondary}`
                }`}
              ></div>

              {/* Circular border - we'll animate this with anime.js */}
              <div
                id="rotating-border"
                className={`profile-image-container absolute -inset-2 opacity-0 rounded-full ${
                  themeColors.accent === 'yellow-500'
                    ? 'bg-[conic-gradient(from_0deg,transparent,#F59E0BCC,transparent)]'
                    : themeColors.accent === 'red-500' || themeColors.accent === 'rose-500'
                      ? 'bg-[conic-gradient(from_0deg,transparent,#F87171CC,transparent)]'
                    : themeColors.accent === 'blue-500'
                      ? 'bg-[conic-gradient(from_0deg,transparent,#3B82F6CC,transparent)]'
                    : themeColors.accent === 'purple-500'
                      ? 'bg-[conic-gradient(from_0deg,transparent,#8B5CF6CC,transparent)]'
                    : 'bg-[conic-gradient(from_0deg,transparent,var(--accent-color-transparent),transparent)]'
                }`}
              ></div>

              {/* Main container with profile image - no borders */}
              <div
                className={`profile-image-container w-72 md:w-72 relative z-10 shadow-2xl backdrop-blur-sm opacity-0 hover:scale-105 transition-transform duration-300 rounded-full animate-item ${
                  themeColors.accent === 'yellow-500'
                    ? 'bg-gradient-to-br from-amber-800 to-yellow-900'
                    : themeColors.accent === 'red-500' || themeColors.accent === 'rose-500'
                      ? 'bg-gradient-to-br from-red-800 to-rose-900'
                    : themeColors.accent === 'blue-500'
                      ? 'bg-gradient-to-br from-blue-800 to-blue-900'
                    : themeColors.accent === 'purple-500'
                      ? 'bg-gradient-to-br from-purple-800 to-purple-900'
                    : `bg-gradient-to-br ${themeColors.primary}`
                }`}
                style={{
                  aspectRatio: '1/1' // Perfect circle requires 1:1 aspect ratio
                }}
              >
                <div className={`w-full h-full flex items-center justify-center overflow-hidden rounded-full ${
                  themeColors.accent === 'yellow-500'
                    ? 'bg-gradient-to-br from-amber-600 to-yellow-700'
                    : themeColors.accent === 'red-500' || themeColors.accent === 'rose-500'
                      ? 'bg-gradient-to-br from-red-500 to-rose-600'
                    : themeColors.accent === 'blue-500'
                      ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                    : themeColors.accent === 'purple-500'
                      ? 'bg-gradient-to-br from-purple-500 to-purple-600'
                    : `bg-gradient-to-br ${themeColors.secondary}`
                }`}
                >
                  <img
                    src={profileImage}
                    alt="Rohit Kumar - .NET Developer"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </section>
  );
};

export default Hero;
